import type { Metadata } from "next";
import { Poppins, Inter } from "next/font/google";
import "./globals.css";
import ClientErrorSuppression from "../components/ClientErrorSuppression";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "800"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800"],
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON> | Backend Developer Portfolio",
  description: "Backend Developer | System Architect | Cloud-Native Specialist - Building robust server-side solutions, APIs, and database architectures for scalable applications",
};

export const viewport = {
  width: "device-width",
  initialScale: 1.0,
  maximumScale: 1.0,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${poppins.variable} ${inter.variable} font-poppins antialiased`}
      >
        <ClientErrorSuppression />
        {children}
      </body>
    </html>
  );
}
