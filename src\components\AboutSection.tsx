import React, { useState, useEffect, useRef } from 'react';
import { motion, useInView, useAnimation } from 'framer-motion';
import DirectionalScrollAnimation from './DirectionalScrollAnimation';
import { useDirectionalInView } from '@/hooks/useDirectionalInView';
import { FaEye as EyeIcon, FaCode as CodeIcon, FaFileAlt as DocumentIcon, FaTrophy as TrophyIcon } from 'react-icons/fa';
import GlowingOrbsBackground from './GlowingOrbsBackground';
import SplineWebComponent from './SplineWebComponent';

// Enhanced Animated Counter Component
const AnimatedCounter = ({ target, duration = 1200, isVisible }: { target: number; duration?: number; isVisible: boolean }) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!isVisible) {
      setCount(0); // Reset counter when not visible
      return;
    }

    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);

      // Smoother easing function for faster, more natural animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      setCount(Math.floor(target * easeOutQuart));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    // Start immediately without delay for smoother experience
    animationFrame = requestAnimationFrame(animate);

    return () => {
      cancelAnimationFrame(animationFrame);
    };
  }, [target, duration, isVisible]);

  return <span>{count}</span>;
};

const AboutSection = () => {
  const statsRef = useRef(null);
  const { ref: statsDirectionalRef, isInView: isStatsInView } = useDirectionalInView({
    threshold: 0.3,
    triggerOnce: false,
    requireDownwardScroll: false
  });
  const controls = useAnimation();
  const sectionRef = useRef<HTMLDivElement>(null);

  // Enhanced animation variants inspired by eki.my.id
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 1.0,
        ease: [0.25, 0.1, 0.25, 1],
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const titleVariants = {
    hidden: { opacity: 0, y: 40 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 1.6,
        ease: [0.25, 0.1, 0.25, 1]
      }
    }
  };

  const subtitleVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 1.4,
        delay: 0.4,
        ease: [0.25, 0.1, 0.25, 1]
      }
    }
  };

  const textVariants = {
    hidden: { opacity: 0, x: -30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 1.4,
        ease: [0.25, 0.1, 0.25, 1]
      }
    }
  };

  const buttonVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };



  const statsContainerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const statsCardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 1.2,
        ease: [0.25, 0.1, 0.25, 1]
      }
    }
  };

  const statsData = [
    {
      icon: CodeIcon,
      number: 5,
      label: "TOTAL PROJECTS",
      description: "Innovative web solutions crafted",
      suffix: ""
    },
    {
      icon: TrophyIcon,
      number: 2,
      label: "CERTIFICATES",
      description: "Professional skills validated",
      suffix: ""
    },
    {
      icon: EyeIcon,
      string: "Student",
      label: "YEARS OF EXPERIENCE",
      description: "Continuous learning journey",
      suffix: ""
    }
  ];

  return (
    <section id="about-section" className="eki-section relative" ref={sectionRef}>
      <GlowingOrbsBackground sectionRef={sectionRef} />
      <div className="eki-container relative z-10">
        {/* Section Header with Enhanced Animations */}
        <div className="max-w-4xl mx-auto">
          <DirectionalScrollAnimation
            animation="custom"
            duration={1.2}
            delay={0}
            triggerOnce={false}
            requireDownwardScroll={false}
            customVariants={containerVariants}
            className="text-center mb-16"
          >
            <DirectionalScrollAnimation
              animation="custom"
              duration={1.2}
              delay={0.2}
              triggerOnce={false}
              requireDownwardScroll={false}
              customVariants={titleVariants}
            >
              <h2 className="text-3xl md:text-3xl lg:text-3xl font-bold mb-6">
                <span className="text-white-smoke" style={{fontSize: "6.5rem"}}>About </span>
                <span className="gradient-text" style={{fontSize: "6.5rem"}}>Me</span>
              </h2>
            </DirectionalScrollAnimation>
            <DirectionalScrollAnimation
              animation="custom"
              duration={1}
              delay={0.5}
              triggerOnce={false}
              requireDownwardScroll={false}
              customVariants={subtitleVariants}
            >
              <p className="text-2xl text-royal-purple font-medium">
                ✨ Transforming ideas into digital experiences ✨
              </p>
            </DirectionalScrollAnimation>
          </DirectionalScrollAnimation>
        </div>

        {/* Main Content with Enhanced Staggered Animations - Full Width Layout */}
        <div className="mb-20 w-full">
          <div className="grid lg:grid-cols-12 gap-6 items-center w-full px-4 lg:px-8">
            {/* Left Side - Spline Model (4 columns on large screens) */}
            <div className="hidden lg:flex lg:col-span-4 justify-center items-center">
              <SplineWebComponent />
            </div>
            {/* Right Side - Content (8 columns) */}
            <div className="w-full lg:col-span-8 lg:pl-12">
              <DirectionalScrollAnimation
                animation="custom"
                duration={1.4}
                delay={0.2}
                triggerOnce={false}
                requireDownwardScroll={false}
                customVariants={containerVariants}
                className="space-y-8"
              >
                <div className="ml-32">
                  {/* Heading section with glow effect */}
                  <div className="relative mb-8">
                    {/* Background glow effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 blur-3xl opacity-30 rounded-full transform scale-150 -z-10"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-400/15 to-pink-400/15 blur-2xl opacity-25 rounded-full transform scale-125 -z-10"></div>
                    <div className="absolute inset-0 bg-purple-500/10 blur-xl opacity-20 rounded-full transform scale-110 -z-10"></div>
                    <DirectionalScrollAnimation
                      animation="custom"
                      duration={1}
                      delay={0.2}
                      triggerOnce={false}
                      requireDownwardScroll={false}
                      customVariants={textVariants}
                    >
                      <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 relative z-10 px-4 py-3 leading-tight" style={ {marginLeft: "2rem"}}>
                        <div className="text-electric-indigo" style={{width: "500px",marginLeft: "7rem"}}>Hello, I'm</div>
                        <div className="text-white-smoke" style={{width: "500px",marginLeft: "7rem"}}>Jugal Soni</div>
                      </h1>
                    </DirectionalScrollAnimation>
                  </div>
                  {/* Paragraph section without glow */}
                  <DirectionalScrollAnimation
                    animation="custom"
                    duration={1}
                    delay={0.6}
                    triggerOnce={false}
                    requireDownwardScroll={false}
                    customVariants={textVariants}
                  >
                    <p className="text-[1.5rem] md:text-[1.5rem] lg:text-[1.5rem] 2xl:text-[1.5rem] text-gray-400 leading-relaxed mb-8 font-normal max-w-none" style={{width: "700px", marginLeft: "10rem"}}>
                      I'm a passionate Computer Science student with a strong interest in backend development. I enjoy building robust, scalable systems and crafting clean, efficient server-side logic that powers great user experiences. My focus is on developing reliable APIs, managing databases, and designing architecture that supports high-performance applications.
                    </p>
                  </DirectionalScrollAnimation>
                </div>
                {/* Action Buttons with Enhanced Animations */}
                <div className="flex mt-10 pl-[18rem]">
                  <DirectionalScrollAnimation
                    animation="custom"
                    duration={0.6}
                    delay={0.8}
                    triggerOnce={false}
                    requireDownwardScroll={false}
                    customVariants={containerVariants}
                    className="flex gap-6 ml-0"
                  >
                    {/* Download CV Button */}
                    <motion.a
                      href="https://drive.google.com/drive/folders/1Iekh53fy38I8-j7hMDM15qRCOqkNXUr3?usp=drive_link"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 px-6 py-3 w-106 rounded-lg bg-gradient-to-r from-[#7B61FF] to-[#AE67FA] text-white font-semibold shadow-md hover:shadow-purple-500/30 transition duration-300 cursor-pointer"
                      initial={{ opacity: 0, y: 35 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
                      whileHover={{
                        scale: 1.05,
                        y: -2,
                        boxShadow: "0 10px 30px rgba(124, 58, 237, 0.4)"
                      }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <DocumentIcon className="w-5 h-5 text-white " />
                      <span className="whitespace-nowrap text-2xl">Download CV</span>
                    </motion.a>
                    {/* View Projects Button */}
                    <motion.a
                      href="#portfolio-showcase"
                      className="flex items-center gap-2 px-6 py-3 w-106 rounded-lg border-2 border-[#AE67FA] text-[#AE67FA] font-semibold text-[25px] hover:bg-[#ae67fa14] transition duration-500 whitespace-nowrap cursor-pointer"
                      initial={{ opacity: 0, y: 35 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: 0.2, ease: [0.25, 0.46, 0.45, 0.94] }}
                      whileHover={{
                        scale: 1.05,
                        y: -2,
                        transition: { duration: 0.3, ease: "easeInOut" }
                      }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <CodeIcon className="w-5 h-5 text-[#AE67FA]" />
                      <span className="whitespace-nowrap">View Projects</span>
                    </motion.a>
                  </DirectionalScrollAnimation>
                </div>
              </DirectionalScrollAnimation>
            </div>
          </div>
        </div>
        {/* Enhanced Stats Section - EKI Style */}
        <div className="max-w-7xl mx-auto">
          <div ref={statsDirectionalRef as React.RefObject<HTMLDivElement>} className="flex justify-center items-center w-full max-w-none mx-auto px-4 gap-8">
            {statsData.map((stat, index) => (
              <DirectionalScrollAnimation
                key={index}
                animation="custom"
                duration={1.4}
                delay={index * 0.2 + 0.3}
                triggerOnce={false}
                requireDownwardScroll={false}
                customVariants={statsCardVariants}
              >
                <motion.div
                  className="relative bg-[#18122bcc] border border-[#655dbb44] rounded-2xl p-8 min-w-[340px] max-w-[420px] flex flex-col justify-between shadow-xl overflow-hidden"
                  style={{ minHeight: '180px', backdropFilter: 'blur(12px)', minWidth: '650px' }}
                  whileHover={{
                    y: -4,
                    scale: 1.01,
                    boxShadow: "0 8px 32px 0 #655dbb33",
                    borderColor: "#655dbb99",
                    transition: { duration: 0.3, ease: "easeInOut" }
                  }}
                >
                  {/* Background gradient effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-electric-indigo/5 to-royal-purple/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ width: "500px" }}></div>
                  {/* Content container with horizontal layout */}
                  <div className="relative z-10 flex items-center justify-between">
                    {/* Left side - Icon */}
                    <DirectionalScrollAnimation
                      animation="custom"
                      duration={0.4}
                      delay={index * 0.05}
                      triggerOnce={false}
                      requireDownwardScroll={false}
                      customVariants={{
                        hidden: { scale: 0, rotate: -90 },
                        visible: { scale: 1, rotate: 0 }
                      }}
                      className="text-3xl text-electric-indigo/80 group-hover:text-electric-indigo transition-colors duration-300"
                    >
                      <stat.icon />
                    </DirectionalScrollAnimation>
                    {/* Right side - Number */}
                    <DirectionalScrollAnimation
                      animation="custom"
                      duration={0.3}
                      delay={index * 0.05 + 0.1}
                      triggerOnce={false}
                      requireDownwardScroll={false}
                      customVariants={{
                        hidden: { opacity: 0, scale: 0.8 },
                        visible: { opacity: 1, scale: 1 }
                      }}
                      className="text-6xl font-bold text-white group-hover:text-electric-indigo transition-colors duration-300"
                    >
                      {stat.string ? stat.string : <AnimatedCounter target={typeof stat.number === 'number' ? stat.number : 0} isVisible={isStatsInView} duration={800} />}{stat.suffix}
                    </DirectionalScrollAnimation>
                  </div>
                  {/* Label and description (only once) */}
                  <div className="mt-6 space-y-2">
                    <DirectionalScrollAnimation
                      animation="custom"
                      duration={0.6}
                      delay={index * 0.2 + 1}
                      triggerOnce={false}
                      requireDownwardScroll={false}
                      customVariants={{
                        hidden: { opacity: 0, y: 20 },
                        visible: { opacity: 1, y: 0 }
                      }}
                      className="text-xl font-semibold text-white/90 uppercase tracking-wider"
                    >
                      {stat.label}
                    </DirectionalScrollAnimation>
                    <DirectionalScrollAnimation
                      animation="custom"
                      duration={0.6}
                      delay={index * 0.2 + 1.2}
                      triggerOnce={false}
                      requireDownwardScroll={false}
                      customVariants={{
                        hidden: { opacity: 0, y: 20 },
                        visible: { opacity: 1, y: 0 }
                      }}
                      className="text-2xl text-gray-400 group-hover:text-gray-300 transition-colors duration-300"
                    >
                      {stat.description}
                    </DirectionalScrollAnimation>
                  </div>
                  {/* Arrow icon (larger) */}
                  <div className="absolute bottom-4 right-6 text-white/30 text-3xl group-hover:text-[#655dbb] transition-colors duration-300 select-none">↗</div>
                </motion.div>
              </DirectionalScrollAnimation>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;

