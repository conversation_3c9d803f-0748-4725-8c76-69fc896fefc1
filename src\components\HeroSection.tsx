'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { CodeIcon, UserIcon, HeartIcon, FaGithub, FaLinkedin } from './icons';
import ThreeModelScene, { SoftwareSceneThree } from './ThreeModel';
import Spline from '@splinetool/react-spline';

// Cycling Typewriter Effect Component
const CyclingTypewriter: React.FC<{
  texts: string[];
  typingSpeed?: number;
  deletingSpeed?: number;
  pauseDuration?: number;
  className?: string;
}> = ({
  texts,
  typingSpeed = 100,
  deletingSpeed = 50,
  pauseDuration = 2000,
  className = ""
}) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [currentText, setCurrentText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  useEffect(() => {
    const currentFullText = texts[currentTextIndex];

    if (isPaused) {
      const pauseTimeout = setTimeout(() => {
        setIsPaused(false);
        setIsDeleting(true);
      }, pauseDuration);
      return () => clearTimeout(pauseTimeout);
    }

    if (!isDeleting && currentText === currentFullText) {
      setIsPaused(true);
      return;
    }

    if (isDeleting && currentText === '') {
      setIsDeleting(false);
      setCurrentTextIndex((prev) => (prev + 1) % texts.length);
      return;
    }

    const timeout = setTimeout(() => {
      if (isDeleting) {
        setCurrentText(currentFullText.substring(0, currentText.length - 1));
      } else {
        setCurrentText(currentFullText.substring(0, currentText.length + 1));
      }
    }, isDeleting ? deletingSpeed : typingSpeed);

    return () => clearTimeout(timeout);
  }, [currentText, currentTextIndex, isDeleting, isPaused, texts, typingSpeed, deletingSpeed, pauseDuration]);

  return (
    <span className={`${className} inline-block`} style={{ margin: 0, padding: 0 }}>
      {currentText}
      <span className="animate-pulse text-electric-indigo ml-1">|</span>
    </span>
  );
};

// Only one HeroSection component should exist
const HeroSection = () => {
  const splineRef = useRef<any>(null);

  // Remove the IntersectionObserver logic since Spline React does not support emitEvent

  const containerVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 1.6,
        ease: [0.25, 0.1, 0.25, 1],
        delayChildren: 0.6,
        staggerChildren: 0.4
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 1.4,
        ease: [0.25, 0.1, 0.25, 1]
      }
    }
  };

  const buttonVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "backOut",
        delay: 1
      }
    },
    hover: {
      scale: 1.03,
      transition: {
        duration: 0.4,
        ease: "easeInOut"
      }
    },
    tap: { scale: 0.95 }
  };

  const techStackVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 1.2,
        staggerChildren: 0.2
      }
    }
  };

  const techItemVariants = {
    hidden: { y: 20, opacity: 0, scale: 0.8 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.7,
        ease: "backOut"
      }
    },
    hover: {
      scale: 1.05,
      y: -2,
      boxShadow: "0 10px 25px rgba(124, 58, 237, 0.3)",
      transition: { duration: 0.3, ease: "easeInOut" }
    },
    tap: { scale: 0.95 }
  };

  const badgeVariants = {
    hidden: { opacity: 0, scale: 0.5, y: -20 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "backOut",
        delay: 0.3
      }
    },
    hover: { scale: 1.05, y: -2, transition: { duration: 0.3 } }
  };

  const titleVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 1.8,
        ease: [0.25, 0.1, 0.25, 1],
        delay: 0.6
      }
    }
  };

  const subtitleVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 1.1,
        ease: [0.25, 0.46, 0.45, 0.94],
        delay: 0.7
      }
    }
  };

  const scrollToContent = () => {
    const nextSection = document.getElementById('about-section');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="min-h-screen flex items-center relative eki-section py-16 lg:py-24 overflow-hidden">
      <div className="relative z-10 w-full max-w-none mx-0 px-4 lg:px-8 xl:px-12">
        <div className="grid lg:grid-cols-[1.2fr_0.8fr] gap-8 lg:gap-16 xl:gap-20 items-center min-h-[80vh] w-full">
          {/* Left Content - Text Section */}
          <motion.div
            className="order-1 lg:order-1 space-y-10 lg:space-y-10 flex flex-col justify-center max-w-none pl-0 lg:pl-8 xl:pl-16"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Ready to Innovate Badge */}
            <motion.div
              className="inline-flex items-center px-6 lg:px-12 py-4 lg:py-6 rounded-3xl relative gap-3 lg:gap-5 bg-black/40 backdrop-blur-lg border border-white/20 border-royal-purple/40 neon-glow text-xl lg:text-3xl w-fit self-start"
              variants={badgeVariants}
              whileHover={{ scale: 1.05, y: -2, transition: { duration: 0.3 } }}
              whileTap={{ scale: 0.95 }}
            >
              ✨ Ready to Innovate
            </motion.div>

            {/* Main Title */}
            <motion.div variants={titleVariants} className="space-y-6 lg:space-y-6 text-left">
              <h1 className="text-6xl md:text-6xl lg:text-6xl xl:text-[7rem] font-bold leading-tight whitespace-nowrap">
                <motion.span
                  className="text-white-smoke inline-block mr-4 backend-glow"
                  variants={itemVariants}
                  style={{
                    textShadow: '0 0 10px #a855f7, 0 0 20px #9333ea, 0 0 30px #7c3aed',
                  }}
                >
                  Backend
                </motion.span>
                <motion.span
                  className="gradient-text inline-block developer-glow"
                  variants={itemVariants}
                  style={{
                    textShadow: '0 0 15px #a855f7, 0 0 25px #9333ea, 0 0 35px #7c3aed'
                  }}
                >
                  Developer
                </motion.span>
              </h1>
            </motion.div>

            {/* Subtitle with Cycling Typewriter */}
            <motion.div
              className="w-full space-y-8"
              variants={subtitleVariants}
            >
              <CyclingTypewriter
                texts={[
                  "CSE Student",
                  "Backend Developer",
                  "AI & ML Engineer",
                  "Full Stack Developer",
                  "ML Engineer"
                ]}
                typingSpeed={80}
                deletingSpeed={40}
                pauseDuration={2500}
                className="text-5xl lg:text-6xl xl:text-5xl text-electric-indigo font-medium typewriter-glow"
              />
              <p className="text-text-muted text-xl lg:text-2xl xl:text-3xl leading-relaxed max-w-2xl ">
                Crafting scalable backend solutions.
              </p>
            </motion.div>
            {/* Tech Stack */}
            <motion.div
              className="space-y-8"
              variants={techStackVariants}
            >
              <div className="flex flex-wrap gap-6 lg:gap-8 justify-start">
                {['Node.js', 'Python', 'MongoDB', 'Express.js'].map((tech, index) => (
                  <motion.span
                    key={tech}
                    className="px-6 lg:px-8 py-3 lg:py-4 bg-black/40 text-white-smoke rounded-xl text-lg lg:text-xl font-medium border border-white/20 border-royal-purple/40 neon-glow backdrop-blur-lg hover:bg-royal-purple/20 transition-all duration-300"
                    variants={techItemVariants}
                  whileHover={{
                    scale: 1.05,
                    y: -2,
                    boxShadow: "0 10px 25px rgba(124, 58, 237, 0.3)"
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  {tech}
                </motion.span>
              ))}
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              className="flex flex-wrap gap-6 lg:gap-8 justify-start pt-4"
              variants={buttonVariants}
            >
              <motion.a
                href="#portfolio-showcase"
                className="group relative px-6 lg:px-8 py-3 lg:py-4 glass text-white font-semibold rounded-xl text-lg lg:text-xl hover:bg-black/50 border border-white/10 border-royal-purple/20 neon-glow button-glow overflow-hidden hover:shadow-[0_0_30px_rgba(124,58,237,0.6),0_0_60px_rgba(168,85,247,0.4),0_0_90px_rgba(139,92,246,0.2)] transition-all duration-300 min-w-[140px] text-center"
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
              >
                {/* Animated border glow effect - lighter and more subtle */}
                <div className="absolute inset-0 rounded-lg bg-black/30 backdrop-blur-lg border border-white/10 border-royal-purple/30 neon-glow via-electric-indigo/20 to-royal-purple/20 opacity-0 group-hover:opacity-60 transition-opacity " />
                <div className="absolute inset-[1px] rounded-lg bg-rich-black/95 group-hover:bg-rich-black/90 transition-colors duration-300" />

                {/* Animated border - more subtle */}
                <div className="absolute inset-0 rounded-lg border border-transparent bg-black/30 backdrop-blur-lg border border-white/10 border-royal-purple/30 neon-glow via-electric-indigo/40 to-royal-purple/40 bg-clip-border opacity-0 group-hover:opacity-70 transition-opacity  text-xl"
                     style={{
                       background: 'linear-gradient(90deg, rgba(124, 58, 237, 0.4), rgba(139, 92, 246, 0.4), rgba(168, 85, 247, 0.4), rgba(139, 92, 246, 0.4), rgba(124, 58, 237, 0.4))',
                       backgroundSize: '200% 100%',
                       animation: 'gradient-shift 3s ease-in-out infinite',
                     }} />

                <span className="relative z-10 flex items-center gap-2 ">
                  Projects
                  <span className="inline-block transition-all duration-300 group-hover:scale-110 ">
                    <span className="group-hover:hidden">🔗</span>
                    <span className="hidden group-hover:inline">💻</span>
                  </span>
                </span>
              </motion.a>

              <motion.a
                href="#contact-section"
                className="group relative px-6 lg:px-8 py-3 lg:py-4 glass text-white-smoke font-semibold rounded-xl text-lg lg:text-xl hover:bg-black/30 backdrop-blur-lg border border-white/10 border-royal-purple/30 neon-glow duration-300 button-glow overflow-hidden hover:shadow-[0_0_30px_rgba(124,58,237,0.6),0_0_60px_rgba(168,85,247,0.4),0_0_90px_rgba(139,92,246,0.2)] transition-all"
                whileHover="hover"
                whileTap={{ scale: 0.95 }}
              >
                {/* Animated border glow effect - lighter and more subtle */}
                <div className="absolute inset-0 rounded-lg bg-black/30 backdrop-blur-lg border border-white/10 border-royal-purple/30 neon-glow via-electric-indigo/20 to-royal-purple/20 opacity-0 group-hover:opacity-60 transition-opacity duration-500 blur-md" />
                <div className="absolute inset-[1px] rounded-lg bg-rich-black/95 group-hover:bg-rich-black/90 transition-colors duration-300" />

                {/* Animated border - more subtle */}
                <div className="absolute inset-0 rounded-lg border border-transparent bg-gradient-to-r from-royal-purple/40 via-electric-indigo/40 to-royal-purple/40 bg-clip-border opacity-0 group-hover:opacity-70 transition-opacity duration-500"
                     style={{
                       background: 'linear-gradient(90deg, rgba(124, 58, 237, 0.4), rgba(139, 92, 246, 0.4), rgba(168, 85, 247, 0.4), rgba(139, 92, 246, 0.4), rgba(124, 58, 237, 0.4))',
                       backgroundSize: '200% 100%',
                       animation: 'gradient-shift 3s ease-in-out infinite'
                     }} />

                <span className="relative z-10 text-xl">Contact ✉</span>
              </motion.a>
            </motion.div>

            {/* Social Links */}
            <motion.div
              className="space-y-4"
              variants={itemVariants}
            >
              <div className="flex space-x-6 lg:space-x-8">
                {[
                  { icon: 'github', label: 'GitHub', url: 'https://github.com/Jugalsoni18' },
                  { icon: 'linkedin', label: 'LinkedIn', url: 'https://www.linkedin.com/in/jugal-soni-8bb797308?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app' }
                ].map(({ icon, label, url }) => (
                  <motion.a
                    key={label}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group relative w-12 h-12 lg:w-14 lg:h-14 rounded-full flex items-center justify-center text-2xl lg:text-3xl bg-black/40 backdrop-blur-lg border border-white/20 border-royal-purple/40 neon-glow transition-all duration-300 hover:scale-110"
                  style={{
                    boxShadow: '0 0 15px rgba(124, 58, 237, 0.4), 0 0 30px rgba(168, 85, 247, 0.2), 0 0 45px rgba(139, 92, 246, 0.1)'
                  }}
                  whileHover={{
                    scale: 1.15,
                    boxShadow: '0 0 20px rgba(124, 58, 237, 0.6), 0 0 40px rgba(168, 85, 247, 0.3), 0 0 60px rgba(139, 92, 246, 0.2)'
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  {icon === 'github' ? (
                    <FaGithub className="w-6 h-6 lg:w-7 lg:h-7 text-white" />
                  ) : icon === 'linkedin' ? (
                    <FaLinkedin className="w-6 h-6 lg:w-7 lg:h-7 text-white" />
                  ) : (
                    icon
                  )}
                </motion.a>
              ))}
              </div>
            </motion.div>


          </motion.div>

          {/* Right Content: Spline Laptop Model */}
          <motion.div
            id="spline-laptop"
            className="order-2 lg:order-2 flex justify-center lg:justify-end items-center w-full h-full relative overflow-hidden"
            variants={itemVariants}
            initial="hidden"
            animate="visible"
          >
            <div className="w-full max-w-[600px] lg:max-w-[800px] xl:max-w-[900px] h-[400px] lg:h-[600px] xl:h-[700px] spline-viewer bg-transparent relative drop-shadow-[0_0_50px_rgba(124,58,237,0.15)] hover:drop-shadow-[0_0_60px_rgba(124,58,237,0.2)] transition-all duration-500">
              <Spline
                ref={splineRef}
                scene="/models/laptop.splinecode"
                style={{
                  width: '100%',
                  height: '100%',
                  background: 'transparent',
                  display: 'block',
                }}
              />
            </div>
          </motion.div>
        </div>
      </div>
      <style jsx global>{`
        .spline-viewer canvas {
          background: transparent !important;
        }
        .glow-effect {
          box-shadow: 0 0 50px 15px rgba(124, 58, 237, 0.12), 0 0 80px 25px rgba(168, 85, 247, 0.08);
          border-radius: 2rem;
        }
      `}</style>
    </section>
  );
};

export default HeroSection;
