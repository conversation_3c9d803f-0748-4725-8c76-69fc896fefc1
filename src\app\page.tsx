'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import LandingPage from '@/components/LandingPage';
import Navigation from '@/components/Navigation';
import AboutSection from '@/components/AboutSection';
import PortfolioShowcase from '@/components/PortfolioShowcase';
import ContactSection from '@/components/ContactSection';
import ScrollGlow from '@/components/ScrollGlow';
import HeroSection from '@/components/HeroSection';

export default function Home() {
  const [showLanding, setShowLanding] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Initialize directional scroll animations (AOS disabled for better control)
  useEffect(() => {
    // Disable AOS to prevent conflicts with our directional scroll system
    // We'll use our custom DirectionalScrollAnimation components instead
  }, []);

  const handleEnterPortfolio = () => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setTimeout(() => {
      setShowLanding(false);
      setIsTransitioning(false);
    }, 1000);
  };

  useEffect(() => {
    // Prevent scrolling on landing page
    if (showLanding) {
      document.body.style.overflow = 'hidden';
      document.body.style.height = '100vh';
    } else {
      document.body.style.overflow = 'auto';
      document.body.style.height = 'auto';
    }

    return () => {
      // Reset body styles on cleanup
      document.body.style.overflow = 'auto';
      document.body.style.height = 'auto';
    };
  }, [showLanding]);

  return (
    <div className="overflow-x-hidden w-full max-w-full">
      <AnimatePresence mode="wait">
        {showLanding ? (
          <LandingPage key="landing" onEnter={handleEnterPortfolio} />
        ) : (
          <motion.div
            key="portfolio"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="relative min-h-screen overflow-x-hidden"
          >
          {/* Unified Background for entire portfolio */}
          <div className="fixed inset-0 bg-gradient-to-b from-rich-black via-deep-violet/20 to-rich-black" />

          {/* Unified Animated background particles */}
          <div className="fixed inset-0 overflow-hidden">
            {[
              { left: '10%', top: '20%' },
              { left: '20%', top: '50%' },
              { left: '30%', top: '80%' },
              { left: '40%', top: '30%' },
              { left: '50%', top: '60%' },
              { left: '60%', top: '10%' },
              { left: '70%', top: '40%' },
              { left: '80%', top: '70%' },
              { left: '90%', top: '25%' },
              { left: '15%', top: '75%' },
              { left: '25%', top: '35%' },
              { left: '35%', top: '65%' },
              { left: '45%', top: '15%' },
              { left: '55%', top: '45%' },
              { left: '65%', top: '85%' },
              { left: '75%', top: '55%' },
              { left: '85%', top: '5%' },
              { left: '95%', top: '95%' },
              { left: '5%', top: '85%' },
              { left: '15%', top: '15%' }
            ].map((position, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-electric-indigo/30 rounded-full"
                style={{
                  left: position.left,
                  top: position.top,
                }}
                animate={{
                  y: [0, -100, 0],
                  opacity: [0, 1, 0],
                }}
                transition={{
                  duration: 10 + (i % 5),
                  repeat: Infinity,
                  delay: i * 0.5,
                }}
              />
            ))}
          </div>

          {/* Scroll-responsive glow effect */}
          <ScrollGlow />

          <div className="relative z-10">
            <Navigation />
            <main>
              <div id="home">
                <HeroSection />
              </div>
              <div id="about">
                <AboutSection />
              </div>
              <div id="portfolio">
                <PortfolioShowcase />
              </div>
              <div id="contact">
                <ContactSection />
              </div>
            </main>
          </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
